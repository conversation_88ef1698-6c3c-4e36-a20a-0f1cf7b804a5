const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Public webhook endpoint (no authentication required)
router.post('/webhook/billionconnect', (req, res, next) => {
    console.log('=== WEBHOOK ROUTE HIT ===');
    console.log('Request details:', {
        method: req.method,
        path: req.path,
        url: req.url,
        originalUrl: req.originalUrl,
        headers: req.headers,
        body: req.body ? 'Body present' : 'No body'
    });
    next();
}, orderController.handleBillionConnectWebhook);

// Add a simple test endpoint to verify webhook connectivity
router.post('/webhook/billionconnect/test', (req, res) => {
    console.log('=== WEBHOOK TEST ENDPOINT HIT ===');
    console.log('Request details:', {
        method: req.method,
        path: req.path,
        url: req.url,
        originalUrl: req.originalUrl,
        headers: req.headers,
        body: req.body
    });

    res.json({
        success: true,
        message: 'Webhook test endpoint reached successfully',
        timestamp: new Date().toISOString(),
        receivedData: req.body
    });
});

// All other routes require authentication
router.use((req, res, next) => {
    console.log('OrderRoutes auth check:', {
        path: req.path,
        method: req.method,
        originalUrl: req.originalUrl
    });

    // Skip authentication for webhook endpoints
    if ((req.path === '/webhook/billionconnect' || req.path === '/webhook/billionconnect/test') && req.method === 'POST') {
        console.log('Skipping authentication for webhook endpoint:', req.path);
        return next();
    }
    return isAuthenticated(req, res, next);
});

// Admin routes
router.get('/admin/all', isAdmin, orderController.getAllOrders);
router.get('/admin/export', isAdmin, orderController.exportOrders);
router.get('/admin/user/:userId', isAdmin, orderController.getOrdersByUserId);
router.get('/admin/:id', isAdmin, orderController.getAllOrderById);

// User routes
router.get('/', orderController.getUserOrders);
router.post('/', orderController.createOrder);
router.get('/:id', orderController.getOrderById);
router.get('/:id/usage', orderController.getOrderUsage);

module.exports = router;
