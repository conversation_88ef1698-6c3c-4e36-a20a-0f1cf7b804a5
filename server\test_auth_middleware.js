const axios = require('axios');

async function testAuthMiddleware() {
    console.log('Testing authentication middleware behavior...\n');
    
    const baseURL = 'http://localhost:3000';
    
    // Test 1: Test webhook endpoint
    console.log('1. Testing webhook endpoint...');
    try {
        const response = await axios.post(`${baseURL}/api/orders/webhook/billionconnect/test`, {
            test: true
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000
        });
        console.log('✅ Webhook test endpoint accessible');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Webhook test endpoint failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n2. Testing main webhook endpoint...');
    try {
        const response = await axios.post(`${baseURL}/api/orders/webhook/billionconnect`, {
            tradeType: 'N009',
            tradeTime: '2025-06-27 12:00:00',
            tradeData: {
                orderId: 'TEST123',
                channelOrderId: 'TEST_CHANNEL',
                subOrderList: []
            }
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000
        });
        console.log('✅ Main webhook endpoint accessible');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Main webhook endpoint failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n3. Testing protected endpoint (should fail)...');
    try {
        const response = await axios.get(`${baseURL}/api/orders`, {
            timeout: 5000
        });
        console.log('❌ Protected endpoint should have failed but succeeded');
        console.log('Response:', response.data);
    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.log('✅ Protected endpoint correctly requires authentication');
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('❌ Unexpected error on protected endpoint');
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n4. Testing auth endpoint (should be accessible)...');
    try {
        const response = await axios.post(`${baseURL}/api/auth/login`, {
            email: '<EMAIL>',
            password: 'test123'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000
        });
        console.log('Auth endpoint response received (expected to fail with invalid credentials)');
    } catch (error) {
        if (error.response) {
            console.log('✅ Auth endpoint accessible (failed with expected error)');
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('❌ Auth endpoint connection failed');
            console.log('Error:', error.message);
        }
    }
}

testAuthMiddleware().catch(console.error);
