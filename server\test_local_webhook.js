const axios = require('axios');

async function testLocalWebhook() {
    console.log('Testing local webhook endpoint...');
    
    try {
        const response = await axios.post('http://localhost:3000/api/orders/webhook/billionconnect/test', {
            test: true,
            timestamp: new Date().toISOString()
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });
        
        console.log('✅ Local webhook test successful');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Local webhook test failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
}

testLocalWebhook();
