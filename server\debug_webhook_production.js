const axios = require('axios');

// Production webhook debugging script
async function debugWebhookProduction() {
    console.log('🔍 Debugging BillionConnect Webhook in Production\n');
    
    const PRODUCTION_BASE_URL = 'https://api.vizlync.net';
    const WEBHOOK_URL = `${PRODUCTION_BASE_URL}/api/orders/webhook/billionconnect`;
    const TEST_WEBHOOK_URL = `${PRODUCTION_BASE_URL}/api/orders/webhook/billionconnect/test`;
    
    // Test 1: Check if the test endpoint is reachable
    console.log('1. Testing webhook connectivity...');
    try {
        const testResponse = await axios.post(TEST_WEBHOOK_URL, {
            test: true,
            timestamp: new Date().toISOString()
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ Test webhook endpoint reachable');
        console.log('Response:', testResponse.data);
    } catch (error) {
        console.log('❌ Test webhook endpoint failed');
        console.log('Error:', error.response?.data || error.message);
        return;
    }
    
    // Test 2: Test the actual webhook endpoint with sample data
    console.log('\n2. Testing actual webhook endpoint...');
    const sampleWebhookData = {
        tradeType: 'N009',
        tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
        tradeData: {
            orderId: 'TEST_ORDER_ID',
            channelOrderId: 'TEST_CHANNEL_ORDER_ID',
            subOrderList: [{
                iccid: '8988228045500123456',
                qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                apn: 'internet',
                smdpAddress: 'test.example.com',
                lpaString: 'LPA:1$test.example.com$1234-5678-90',
                activationCode: '1234-5678-90'
            }]
        }
    };
    
    try {
        const webhookResponse = await axios.post(WEBHOOK_URL, sampleWebhookData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ Webhook endpoint reachable');
        console.log('Response:', webhookResponse.data);
    } catch (error) {
        console.log('❌ Webhook endpoint failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Status Text:', error.response.statusText);
            console.log('Response Data:', error.response.data);
            console.log('Response Headers:', error.response.headers);
        } else if (error.request) {
            console.log('No response received');
            console.log('Request details:', {
                method: error.config?.method,
                url: error.config?.url,
                headers: error.config?.headers
            });
        } else {
            console.log('Error setting up request:', error.message);
        }
        return;
    }
    
    // Test 3: Check server logs endpoint (if available)
    console.log('\n3. Webhook debugging complete');
    console.log('\nNext steps:');
    console.log('1. Check your production server logs for the debug messages');
    console.log('2. Verify BillionConnect webhook URL configuration');
    console.log('3. Ensure BillionConnect is sending webhooks to the correct URL');
    console.log('4. Check if there are any firewall or proxy issues');
    
    console.log('\nWebhook URLs to verify with BillionConnect:');
    console.log(`Production: ${WEBHOOK_URL}`);
    console.log(`Test: ${TEST_WEBHOOK_URL}`);
}

// Test with a real order ID if provided
async function testWithRealOrder(externalOrderId) {
    console.log(`\n🔍 Testing webhook with real order ID: ${externalOrderId}\n`);
    
    const WEBHOOK_URL = 'https://api.vizlync.net/api/orders/webhook/billionconnect';
    
    // Create webhook data for the real order
    const webhookData = {
        tradeType: 'N009',
        tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
        tradeData: {
            orderId: externalOrderId,
            channelOrderId: `order_test_${Date.now()}`,
            subOrderList: [{
                iccid: '8988228045500123456',
                qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                apn: 'internet',
                smdpAddress: 'test.example.com',
                lpaString: 'LPA:1$test.example.com$1234-5678-90',
                activationCode: '1234-5678-90'
            }]
        }
    };
    
    try {
        console.log('Sending webhook with data:', JSON.stringify(webhookData, null, 2));
        
        const response = await axios.post(WEBHOOK_URL, webhookData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('✅ Webhook sent successfully');
        console.log('Response:', response.data);
        
        // Wait for processing
        console.log('\nWaiting 3 seconds for processing...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
    } catch (error) {
        console.log('❌ Webhook failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        const externalOrderId = args[0];
        await testWithRealOrder(externalOrderId);
    } else {
        await debugWebhookProduction();
    }
}

// Run the script
main().catch(console.error);
