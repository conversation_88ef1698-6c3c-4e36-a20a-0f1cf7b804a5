const axios = require('axios');

async function testProductionAuth() {
    console.log('Testing production authentication middleware behavior...\n');
    
    const baseURL = 'https://esim-management.onrender.com';
    
    // Test 1: Test webhook test endpoint
    console.log('1. Testing webhook test endpoint...');
    try {
        const response = await axios.post(`${baseURL}/api/orders/webhook/billionconnect/test`, {
            test: true,
            timestamp: new Date().toISOString()
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        console.log('✅ Webhook test endpoint accessible');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Webhook test endpoint failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n2. Testing main webhook endpoint...');
    try {
        const response = await axios.post(`${baseURL}/api/orders/webhook/billionconnect`, {
            tradeType: 'N009',
            tradeTime: '2025-06-27 12:00:00',
            tradeData: {
                orderId: 'TEST123',
                channelOrderId: 'TEST_CHANNEL',
                subOrderList: [{
                    iccid: '8988228045500123456',
                    qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                    apn: 'internet',
                    smdpAddress: 'test.example.com',
                    lpaString: 'LPA:1$test.example.com$1234-5678-90',
                    activationCode: '1234-5678-90'
                }]
            }
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        console.log('✅ Main webhook endpoint accessible');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Main webhook endpoint failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n3. Testing with real order ID VLZ2007...');
    try {
        const response = await axios.post(`${baseURL}/api/orders/webhook/billionconnect`, {
            tradeType: 'N009',
            tradeTime: '2025-06-27 12:00:00',
            tradeData: {
                orderId: 'VLZ2007',
                channelOrderId: 'order_8ef58573-525d-11f0-966a-0ae1562662bd_1751024683885',
                subOrderList: [{
                    iccid: '8988228045500123456',
                    qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                    apn: 'internet',
                    smdpAddress: 'test.example.com',
                    lpaString: 'LPA:1$test.example.com$1234-5678-90',
                    activationCode: '1234-5678-90'
                }]
            }
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        console.log('✅ Real order webhook successful');
        console.log('Response:', response.data);
    } catch (error) {
        console.log('❌ Real order webhook failed');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n4. Testing protected endpoint (should fail)...');
    try {
        const response = await axios.get(`${baseURL}/api/orders`, {
            timeout: 10000
        });
        console.log('❌ Protected endpoint should have failed but succeeded');
        console.log('Response:', response.data);
    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.log('✅ Protected endpoint correctly requires authentication');
            console.log('Status:', error.response.status);
        } else {
            console.log('❌ Unexpected error on protected endpoint');
            console.log('Error:', error.message);
        }
    }
    
    console.log('\n5. Testing auth endpoint (should be accessible)...');
    try {
        const response = await axios.post(`${baseURL}/api/auth/login`, {
            email: '<EMAIL>',
            password: 'test123'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        console.log('Auth endpoint response received (expected to fail with invalid credentials)');
    } catch (error) {
        if (error.response) {
            console.log('✅ Auth endpoint accessible (failed with expected error)');
            console.log('Status:', error.response.status);
            if (error.response.status === 401) {
                console.log('Expected 401 - Invalid credentials');
            }
        } else {
            console.log('❌ Auth endpoint connection failed');
            console.log('Error:', error.message);
        }
    }
}

testProductionAuth().catch(console.error);
