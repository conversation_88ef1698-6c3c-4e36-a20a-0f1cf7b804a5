# BillionConnect Webhook Diagnosis

## Issue Summary
Webhooks work in testing but not in production. The order creation succeeds, but the N009 webhook (containing eSIM details) is not being received in production.

## Potential Causes & Solutions

### 1. **Webhook URL Configuration** (Most Likely)
**Problem**: BillionConnect may be configured with the wrong webhook URL for production.

**Check**: 
- Verify with BillionConnect what webhook URL they have configured for your production account
- Ensure it matches: `https://api.vizlync.net/api/orders/webhook/billionconnect`

**Solution**: 
- Contact BillionConnect support to update the webhook URL
- Provide them with the correct production webhook URL

### 2. **Environment-Specific Credentials**
**Problem**: Different API credentials between test and production environments.

**Check**:
- Verify `BILLIONCONNECT_CHANNEL_ID` and `BILLIONCONNECT_APP_SECRET` in production
- Ensure they match the production account, not test account

### 3. **Network/Firewall Issues**
**Problem**: Production server may be blocking incoming webhook requests.

**Check**:
- Verify that your production server can receive external HTTP POST requests
- Check if there are any firewall rules blocking BillionConnect's IP addresses
- Ensure your hosting provider (appears to be api.vizlync.net) allows incoming webhooks

### 4. **SSL/TLS Issues**
**Problem**: BillionConnect may have issues with your SSL certificate.

**Check**:
- Verify your SSL certificate is valid and trusted
- Test webhook endpoint with SSL testing tools

### 5. **Request Processing Issues**
**Problem**: Middleware or authentication blocking webhook requests.

**Status**: ✅ **FIXED** - Added debugging middleware to identify this issue

## Debugging Steps Added

### 1. Enhanced Logging
Added comprehensive logging to:
- Webhook endpoint entry point
- CORS middleware
- Rate limiting middleware  
- Authentication middleware
- Webhook handler function

### 2. Test Endpoint
Added `/api/orders/webhook/billionconnect/test` endpoint for connectivity testing.

### 3. Debug Script
Created `debug_webhook_production.js` to test webhook connectivity.

## How to Use Debug Tools

### 1. Run the debug script:
```bash
node debug_webhook_production.js
```

### 2. Test with a real order ID:
```bash
node debug_webhook_production.js VLZ2006
```

### 3. Check production logs:
Look for these debug messages in your production logs:
- "Webhook endpoint hit:"
- "=== WEBHOOK HANDLER REACHED ==="
- "Skipping CORS for webhook endpoint"
- "Skipping rate limiting for webhook endpoint"
- "Skipping authentication for:"

## Next Steps

1. **Run the debug script** to test connectivity
2. **Check production logs** for debug messages
3. **Contact BillionConnect** to verify webhook URL configuration
4. **Verify network connectivity** from BillionConnect to your server
5. **Test SSL certificate** validity

## Expected Webhook Format

BillionConnect should send webhooks in this format:
```json
{
  "tradeType": "N009",
  "tradeTime": "2025-06-27 10:59:04",
  "tradeData": {
    "orderId": "VLZ2006",
    "channelOrderId": "order_8ef58573-525d-11f0-966a-0ae1562662bd_1751021944250",
    "subOrderList": [{
      "iccid": "8988228045500123456",
      "qrCodeContent": "LPA:1$smdp.example.com$activation-code",
      "apn": "internet",
      "smdpAddress": "smdp.example.com",
      "lpaString": "LPA:1$smdp.example.com$activation-code",
      "activationCode": "activation-code"
    }]
  }
}
```

## Contact Information

When contacting BillionConnect support, provide:
- Your production webhook URL: `https://api.vizlync.net/api/orders/webhook/billionconnect`
- Your channel ID (from environment variables)
- Recent order IDs that should have triggered webhooks
- Request that they verify webhook configuration and test connectivity
