require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const sequelize = require('./src/config/database');
const { createServer } = require('http');
const { Server } = require('socket.io');
const authRoutes = require('./src/routes/authRoutes');
const partnerRoutes = require('./src/routes/partners');
const countryRoutes = require('./src/routes/countries');
const staffRoutes = require('./src/routes/staff');
const walletRoutes = require('./src/routes/walletRoutes');
const centralWalletRoutes = require('./src/routes/centralWalletRoutes');
const esimPlanRoutes = require('./src/routes/esimPlanRoutes');
const providerRoutes = require('./src/routes/providerRoutes');
const orderRoutes = require('./src/routes/orderRoutes');

const partnerDashboardRoutes = require('./src/routes/partnerDashboardRoutes');
const adminDashboardRoutes = require('./src/routes/adminDashboardRoutes');
const cartRoutes = require('./src/routes/cartRoutes');
const knowledgeBaseRoutes = require('./src/routes/knowledgeBaseRoutes');
const notificationRoutes = require('./src/routes/notificationRoutes');
const partnerApiRoutes = require('./src/routes/partnerApiRoutes');
const configRoutes = require('./src/routes/configRoutes');
const apiV1Routes = require('./src/routes/apiV1Routes');
const { loginLimiter, clientApiLimiter } = require('./src/middleware/rateLimiter');
const cronService = require('./src/services/cron.service');
const path = require('path');
const { isAuthenticated } = require('./src/middleware/auth');


// Define allowed origins first
const allowedOrigins = [
    'http://localhost:3000',  // Add local development server
    'http://localhost:5000',
    'http://localhost:5173',  // Vite's default dev server port
    'https://esim.vizlync.net',
    'https://api.vizlync.net',
    'https://esim-management.vercel.app',
    'https://esim-management.onrender.com',
    'https://api-flow-ts.billionconnect.com'  // Add BillionConnect API domain
];

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
    cors: {
        origin: allowedOrigins,
        credentials: true
    },
    // Add connection state recovery to handle reconnections better
    connectionStateRecovery: {
        // the backup duration of the sessions and the packets
        maxDisconnectionDuration: 2 * 60 * 1000,
        // whether to skip middlewares upon successful recovery
        skipMiddlewares: true,
    },
    // Optimize ping timeout and interval to detect disconnections faster
    pingTimeout: 30000,
    pingInterval: 25000,
    // Prevent multiple connections from the same client
    allowEIO3: true,
    transports: ['websocket', 'polling'],
});

// Global rate limiter
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 1000
});

app.set('trust proxy', 1);

// Middleware that should run before any rate limiting or authentication
app.use(express.json({ limit: '10mb' }));
app.use(cookieParser());
app.use(morgan('dev'));

// Add request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
    next();
});

// Configure CORS
app.use((req, res, next) => {
    // Skip CORS for webhook endpoint
    if (req.path === '/api/orders/webhook/billionconnect') {
        console.log('Skipping CORS for webhook endpoint');
        return next();
    }

    const corsOptions = {
        origin: process.env.CORS_ORIGIN || allowedOrigins,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization'],
        credentials: true
    };
    cors(corsOptions)(req, res, next);
});

// Webhook endpoints are handled by orderRoutes - no need to define them here

// Apply rate limiting to all routes except webhook
app.use((req, res, next) => {
    if (req.path === '/api/orders/webhook/billionconnect') {
        console.log('Skipping rate limiting for webhook endpoint');
        return next();
    }
    return clientApiLimiter(req, res, next);
});

// Apply authentication to all routes except webhook and auth routes
app.use('/api', (req, res, next) => {
    // Skip authentication for webhook and auth routes
    console.log('Auth middleware check:', {
        path: req.path,
        method: req.method,
        originalUrl: req.originalUrl,
        isWebhook: req.path === '/orders/webhook/billionconnect',
        isAuth: req.path.startsWith('/auth/')
    });

    if (req.path === '/orders/webhook/billionconnect' || req.path.startsWith('/auth/')) {
        console.log('Skipping authentication for:', req.path);
        return next();
    }
    return isAuthenticated(req, res, next);
});

// Root route
app.get('/', (req, res) => {
    res.json({
        status: 'success',
        message: 'eSIM Management API is running',
        version: '1.0.0',
        documentation: '/api/docs' // for future API documentation
    });
});

// Serve static files from 'public' folder
app.use('/docs', express.static(path.join(__dirname, 'public')));

app.get('/docs', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'partner-api-docs.html'));
  });


// Routes
app.use('/api/auth', authRoutes);
app.use('/api/partners', partnerRoutes);
app.use('/api/countries', countryRoutes);
app.use('/api/staff', staffRoutes);
app.use('/api', walletRoutes);
app.use('/api', centralWalletRoutes);
app.use('/api/esim-plans', esimPlanRoutes);
app.use('/api/providers', providerRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/partner/dashboard', partnerDashboardRoutes);
app.use('/api/admin/dashboard', adminDashboardRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/knowledge-base', knowledgeBaseRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/partner', partnerApiRoutes);
app.use('/api/config', configRoutes);

// API v1 routes
const apiV1Router = express.Router();
// Use the apiV1Routes for API v1 endpoints
apiV1Router.use('/', apiV1Routes);
app.use('/api/v1', apiV1Router);

// WebSocket connection handling
io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    // Check if user is already connected with another socket
    socket.on('join', (userId) => {
        if (!userId) return;

        // Get all socket instances for this user
        const userRoom = `user_${userId}`;
        const roomSockets = io.sockets.adapter.rooms.get(userRoom);

        // If this user already has an active socket connection
        if (roomSockets && roomSockets.size > 0) {
            console.log(`User ${userId} already has ${roomSockets.size} active connections`);
        }

        // Join the room anyway
        socket.join(userRoom);
        console.log(`User ${userId} joined their room`);
    });

    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
    });
});

// Make io accessible to routes
app.set('io', io);

// Catch-all route for unhandled paths
app.use('*', (req, res) => {
    res.status(404).json({
        status: 'error',
        message: 'Route not found',
        requestedPath: req.originalUrl
    });
});

// Add error logging middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: err.message });
});

const startServer = async () => {
    try {
        // Initialize database with retries
        let retries = 2;
        while (retries) {
            try {
                await sequelize.authenticate();
                console.log('Database connection established.');

                // Sync models without foreign key checks
                await sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
                await sequelize.sync();
                await sequelize.query('SET FOREIGN_KEY_CHECKS = 1');

                console.log('Database synchronized successfully');
                break;
            } catch (error) {
                retries -= 1;
                console.log(`Retries left: ${retries}`);
                if (!retries) {
                    throw error;
                }

                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }

        // Start cron jobs
        cronService.startJobs();

        const PORT = process.env.PORT || 3000;
        httpServer.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    } catch (error) {
        console.error('Unable to start server:', error);
        process.exit(1);
    }
};

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM signal received. Shutting down gracefully...');
    cronService.stopJobs();
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT signal received. Shutting down gracefully...');
    cronService.stopJobs();
    process.exit(0);
});

startServer();
